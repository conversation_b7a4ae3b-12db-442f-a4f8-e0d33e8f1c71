{"sections": {"featured-collection": {"disabled": true, "type": "featured-collection", "settings": {"title": "セール商品", "heading_size": "title5", "label_1": "", "label_2": "", "label_3": "", "label_4": "", "label_5": "", "label_6": "", "products_to_show": 5, "columns_desktop": 5, "full_width": true, "columns_mobile": "2", "enable_desktop_slider": true, "enable_mobile_slider": true, "button_text": "すべて表示→", "full_in_mobile": false, "product_image_ratio": "100", "image_fill_type": "cover", "image_grid_shape": "square", "show_secondary_image": true, "padding_top": 20, "padding_bottom": 0, "collection_1": "12267330399678683347744095"}, "blocks": {"block-1": {"type": "image", "settings": {}}, "block-2": {"type": "title", "settings": {}}, "block-3": {"type": "price", "settings": {}}}, "block_order": ["block-1", "block-2", "block-3"], "custom_css": []}, "featured-collection2": {"disabled": false, "type": "featured-collection", "settings": {"title": "売れ筋ランキング", "heading_size": "title5", "label_1": "All", "label_2": "", "label_3": "", "label_4": "", "label_5": "", "label_6": "", "products_to_show": 5, "columns_desktop": 5, "full_width": true, "columns_mobile": "2", "enable_desktop_slider": true, "enable_mobile_slider": true, "button_text": "すべて表示→", "full_in_mobile": false, "product_image_ratio": "100", "image_fill_type": "cover", "image_grid_shape": "square", "show_secondary_image": true, "padding_top": 40, "padding_bottom": 0, "collection_1": "12266502800459597121234095", "collection_6": "12267330400505632323654095", "collection_4": "12266502786952260539874095", "collection_5": "12266502787279248480054095", "collection_2": "12266502786556486013974095", "collection_3": "12266502786950247273894095"}, "blocks": {"block-1": {"type": "image", "settings": {}, "blockId": "block-1", "sectionId": "featured-collection2", "canAddBlock": true, "formatName": "图片", "overLimit": true}, "block-2": {"type": "title", "settings": {}, "blockId": "block-2", "sectionId": "featured-collection2", "canAddBlock": true, "formatName": "标题", "overLimit": true}, "block-3": {"type": "price", "settings": {}, "blockId": "block-3", "sectionId": "featured-collection2", "canAddBlock": true, "formatName": "价格", "overLimit": true}}, "block_order": ["block-1", "block-2", "block-3"], "custom_css": [".button.button--link {padding: 12px 0 !important;}"]}, "logo-list": {"disabled": false, "type": "logo-list", "settings": {"width": "normal", "layout": "vertical", "font_color": "#000000", "icon_color": "#000000", "background_color": "rgba(0,0,0,0)", "mobile_display": "scroll", "style_card": true, "padding_top": 60, "padding_bottom": 60}, "blocks": {"block-2": {"type": "icon", "icon": "image", "settings": {"icon": "chat", "title": "友達紹介", "subtitle": "友達を紹介すると、1000円クーポンをプレゼントします。", "link": "shopline://pages/6880794589562413959", "image": "shopline://shop_images/icon-friend.svg"}, "blockId": "block-2", "sectionId": "logo-list", "canAddBlock": true, "formatName": "友達紹介", "overLimit": true}, "block-3": {"type": "icon", "icon": "image", "settings": {"icon": "pay", "title": "カスタマーサービス", "subtitle": "お客様のお困りごとを迅速に解決いたします。", "link": "https://caguuu.com/pages/faq", "image": "shopline://shop_images/icon-headphone.svg"}, "custom_css": [], "blockId": "block-3", "sectionId": "logo-list", "canAddBlock": true, "formatName": "カスタマーサービス", "overLimit": true}, "block-4": {"type": "icon", "icon": "image", "settings": {"icon": "package", "title": "安全な支払い", "subtitle": "お客様の支払い情報は、安全に処理されます。", "link": "https://caguuu.com/pages/usage-guide#payment", "image": "shopline://shop_images/icon-shield.svg"}, "custom_css": [], "blockId": "block-4", "sectionId": "logo-list", "canAddBlock": true, "formatName": "安全な支払い", "overLimit": true}, "173993767287696135b5": {"type": "icon", "settings": {"icon": "chat", "title": "迅速な無料配送", "subtitle": "すべてのご注文で、無料配送をご利用いただけます。", "link": "https://caguuu.com/policies/shipping-policy", "image": "shopline://shop_images/icon-package.svg"}, "custom_css": [], "blockId": "173993767287696135b5", "sectionId": "logo-list", "canAddBlock": true, "formatName": "迅速な無料配送", "overLimit": true}}, "block_order": ["block-2", "block-4", "173993767287696135b5", "block-3"], "custom_css": ["@media (max-width: 959px) {.logo-list__scroll-wrapper.mobile-display-scroll .logo-list__item-wrapper {width: 41%; } .logo-list.layout-vertical .logo-list__item {padding: 5px; }}"]}, "17273238961427984f2f": {"disabled": false, "type": "featured-collection", "settings": {"title": "注目の新着商品", "heading_size": "title5", "label_1": "All", "label_2": "テーブル", "label_3": "ソファ", "label_4": "収納家具", "label_5": "ベッド", "label_6": "チェア・椅子", "products_to_show": 5, "columns_desktop": 5, "full_width": true, "columns_mobile": "2", "enable_desktop_slider": true, "enable_mobile_slider": true, "button_text": "すべて表示→", "full_in_mobile": false, "product_image_ratio": "100", "image_fill_type": "cover", "image_grid_shape": "square", "show_secondary_image": true, "padding_top": 40, "padding_bottom": 40, "collection_6": "12266502798903174812514095", "collection_3": "12266502799039070239174095", "collection_1": "12266502798241649163714095", "collection_2": "12266502799059035150394095", "collection_5": "12266502787279248480054095", "collection_4": "12266502799191239612684095"}, "blocks": {"1727323896142de3d407": {"type": "image", "settings": {}, "blockId": "1727323896142de3d407", "sectionId": "17273238961427984f2f", "canAddBlock": true, "formatName": "图片", "overLimit": true}, "1727323896142c44e597": {"type": "title", "settings": {}, "blockId": "1727323896142c44e597", "sectionId": "17273238961427984f2f", "canAddBlock": true, "formatName": "标题", "overLimit": true}, "172732389614234bb606": {"type": "price", "settings": {}, "blockId": "172732389614234bb606", "sectionId": "17273238961427984f2f", "canAddBlock": true, "formatName": "价格", "overLimit": true}, "1727323896142375818f": {"type": "quick_add_button", "settings": {}, "blockId": "1727323896142375818f", "sectionId": "17273238961427984f2f", "canAddBlock": true, "formatName": "快速加购按钮", "overLimit": true}}, "block_order": ["1727323896142de3d407", "1727323896142c44e597", "172732389614234bb606", "1727323896142375818f"], "custom_css": [".button.button--link {padding: 12px 0 !important;}"]}, "1727331691879fda4a51": {"disabled": false, "type": "blog", "settings": {"title": "お知らせ", "title_align": "left", "blog_collection": "66eb96d0da46c00fb401ba21", "limit": 3, "pc_cols": 3, "mobile_cols": 1, "enable_mobile_slide": false, "mobile_pagination_style": "simple", "is_show_date": true, "is_show_author": false, "is_show_cover": false, "image_cover_ratio": "0.75", "is_show_desc": false, "btn_text": "すべて表示", "color_scheme": "1", "padding_top": 40, "padding_bottom": 40}, "blocks": {}, "block_order": [], "custom_css": []}, "1733212444478604afeb": {"disabled": false, "type": "custom-html", "settings": {"html": "<!-- 主容器 -->\n<div class=\"page-width\">\n    <div class=\"kv-container\">\n        <!-- 轮播图部分 -->\n        <div class=\"kv-slideshow\">\n            <div class=\"kv-slides\">\n                <!-- 轮播图1 -->\n                <a href=\"/collections/big-sale\">\n                    <picture>\n                        <source media=\"(max-width: 768px)\"\n                            srcset=\"https://img.myshopline.com/image/store/1726464192427/kv-summer-sale.jpeg?w=1125&h=633\">\n                        <img src=\"https://img.myshopline.com/image/store/1726464192427/kv-summer-sale-pc.jpeg?w=2072&h=915\" alt=\"夏先取りセール\">\n                    </picture>\n                </a>\n                <!-- 轮播图2 -->\n                <a href=\"/pages/original-mettress-series\">\n                    <picture>\n                        <source media=\"(max-width: 768px)\"\n                            srcset=\"https://img.myshopline.com/image/store/1726464192427/kv-Grand-Form-Special-0.jpeg?w=1125&h=633\">\n                        <img src=\"https://img.myshopline.com/image/store/1726464192427/kv-Grand-Form-pc-2.jpeg?w=2072&h=915\" alt=\"オリジナルマットレス\">\n                    </picture>\n                </a>\n                <!-- 轮播图3 -->\n                <a href=\"/pages/custom-made-sofa\">\n                    <picture>\n                        <source media=\"(max-width: 768px)\"\n                            srcset=\"https://img.myshopline.com/image/store/1726464192427/kv-custom-mad-sofa-Special.jpeg?w=1125&h=633\">\n                        <img src=\"https://img.myshopline.com/image/store/1726464192427/kv-custom-mad-sofa-pc-0.jpeg?w=2072&h=915\" alt=\"オーダーメイドソファ\">\n                    </picture>\n                </a>\n            </div>\n            <!-- 轮播图指示器 -->\n            <div class=\"dots\">\n                <div class=\"dot active\" onclick=\"currentSlide(0)\"></div>\n                <div class=\"dot\" onclick=\"currentSlide(1)\"></div>\n                <div class=\"dot\" onclick=\"currentSlide(2)\"></div>\n            </div>\n        </div>\n        <!-- 侧边图片区域 -->\n        <div class=\"side-image\">\n            <a href=\"/pages/caguuu-jackery\" class=\"hotspot-top\">\n                <picture>\n                    <source media=\"(max-width: 768px)\"\n                        srcset=\"https://img.myshopline.com/image/store/1726464192427/top-jackery-sp-6.jpeg?w=380&h=180\">\n                    <img src=\"https://img.myshopline.com/image/store/1726464192427/top-jackery-pc.jpeg?w=563&h=375\" alt=\"CAGUUU&Jackery\">\n                </picture>\n            </a>\n            <a href=\"/collections/express-delivery\" class=\"hotspot-bottom\">\n                <picture>\n                    <source media=\"(max-width: 768px)\"\n                        srcset=\"https://img.myshopline.com/image/store/1726464192427/side-img-2-sp.jpeg?w=285&h=135\">\n                    <img src=\"https://img.myshopline.com/image/store/1726464192427/side-img-2-pc.jpeg?w=563&h=375\" alt=\"お急ぎ便対応商品\">\n                </picture>\n            </a>\n        </div>\n    </div>\n</div>\n\n<script>\n    // 轮播图相关变量\n    const SLIDE_INTERVAL = 3000; // 轮播间隔时间（毫秒）\n    const SLIDE_WIDTH = 33.333; // 每个轮播图的宽度百分比\n    let slideIndex = 0;\n    const slides = document.querySelector('.kv-slides');\n    const dots = document.querySelectorAll('.dot');\n    const totalSlides = 3;\n\n    // 触摸和拖拽相关变量\n    let startX = 0;\n    let currentX = 0;\n    let isDragging = false;\n    let autoPlayTimer = null;\n\n    /**\n     * 自动轮播函数\n     */\n    function showSlides() {\n        if (!isDragging) {\n            slideIndex = (slideIndex + 1) % totalSlides;\n            updateSlidePosition();\n            updateDots();\n        }\n        autoPlayTimer = setTimeout(showSlides, SLIDE_INTERVAL);\n    }\n\n    /**\n     * 更新轮播图位置\n     */\n    function updateSlidePosition(transition = true) {\n        slides.style.transition = transition ? 'transform 0.3s ease-out' : 'none';\n        slides.style.transform = `translateX(${-slideIndex * SLIDE_WIDTH}%)`;\n    }\n\n    /**\n     * 更新指示器状态\n     */\n    function updateDots() {\n        dots.forEach(dot => dot.classList.remove('active'));\n        dots[slideIndex].classList.add('active');\n    }\n\n    /**\n     * 切换到指定轮播图\n     * @param {number} index - 目标轮播图索引\n     */\n    function currentSlide(index) {\n        slideIndex = index;\n        updateSlidePosition();\n        updateDots();\n    }\n\n    /**\n     * 处理触摸开始事件\n     * @param {TouchEvent} e - 触摸事件对象\n     */\n    function handleTouchStart(e) {\n        isDragging = true;\n        startX = e.touches[0].clientX;\n        currentX = startX;\n        clearTimeout(autoPlayTimer);\n    }\n\n    /**\n     * 处理触摸移动事件\n     * @param {TouchEvent} e - 触摸事件对象\n     */\n    function handleTouchMove(e) {\n        if (!isDragging) return;\n        \n        currentX = e.touches[0].clientX;\n        const diff = currentX - startX;\n        const offset = (diff / slides.offsetWidth) * 100;\n        \n        slides.style.transition = 'none';\n        slides.style.transform = `translateX(${-slideIndex * SLIDE_WIDTH + offset}%)`;\n    }\n\n    /**\n     * 处理触摸结束事件\n     */\n    function handleTouchEnd() {\n        if (!isDragging) return;\n        \n        const diff = currentX - startX;\n        const threshold = slides.offsetWidth * 0.05; // 5%的滑动阈值\n        \n        if (Math.abs(diff) > threshold) {\n            if (diff > 0 && slideIndex > 0) {\n                slideIndex--;\n            } else if (diff < 0 && slideIndex < totalSlides - 1) {\n                slideIndex++;\n            }\n        }\n        \n        updateSlidePosition();\n        updateDots();\n        isDragging = false;\n        autoPlayTimer = setTimeout(showSlides, SLIDE_INTERVAL);\n    }\n\n    // 添加触摸事件监听\n    slides.addEventListener('touchstart', handleTouchStart, { passive: true });\n    slides.addEventListener('touchmove', handleTouchMove, { passive: true });\n    slides.addEventListener('touchend', handleTouchEnd);\n\n    // 添加鼠标事件监听（用于桌面端拖拽）\n    slides.addEventListener('mousedown', (e) => {\n        isDragging = true;\n        startX = e.clientX;\n        currentX = startX;\n        clearTimeout(autoPlayTimer);\n    });\n\n    slides.addEventListener('mousemove', (e) => {\n        if (!isDragging) return;\n        \n        currentX = e.clientX;\n        const diff = currentX - startX;\n        const offset = (diff / slides.offsetWidth) * 100;\n        \n        slides.style.transition = 'none';\n        slides.style.transform = `translateX(${-slideIndex * SLIDE_WIDTH + offset}%)`;\n    });\n\n    slides.addEventListener('mouseup', handleTouchEnd);\n    slides.addEventListener('mouseleave', handleTouchEnd);\n\n    // 启动自动轮播\n    autoPlayTimer = setTimeout(showSlides, SLIDE_INTERVAL);\n</script>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 20}, "blocks": {}, "block_order": [], "custom_css": [".advc-title {font-size: 18px;}", "@media only screen and (max-width: 768px) {.advc-title {font-size: 14px; } .kv-slideshow {aspect-ratio: 1125/633; }}"]}, "173321252133581cc3a4": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n<div class=\"category-container\">\n    <div class=\"category-item\"><a href=\"/collections/sofa\"><img src=\"https://img.myshopline.com/image/store/1726464192427/18-3.jpeg?w=1080&h=1080\" alt=\"\">ソファ</a></div>\n    <div class=\"category-item\"><a href=\"/collections/chairs\"><img src=\"https://img.myshopline.com/image/store/1726464192427/7-14.jpeg?w=1080&h=1080\" alt=\"\">チェア・椅子</a></div>\n    <div class=\"category-item\"><a href=\"/collections/tables\"><img src=\"https://img.myshopline.com/image/store/1726464192427/8-10.jpeg?w=1080&h=1080\" alt=\"\">テーブル</a></div>\n    <div class=\"category-item\"><a href=\"/collections/desk\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-desk.jpg?w=240&h=240\" alt=\"\">デスク・机</a></div>\n    <div class=\"category-item\"><a href=\"/collections/storage\"><img src=\"https://img.myshopline.com/image/store/1726464192427/9-9.jpeg?w=1080&h=1080\" alt=\"\">収納家具</a></div>\n    <div class=\"category-item\"><a href=\"/collections/dresser\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-dresser-6.jpg?w=240&h=240\" alt=\"\">ミラー・ドレッサー</a></div>\n    <div class=\"category-item\"><a href=\"/collections/pet-supplies\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-pet-supplies-6.jpg?w=240&h=240\" alt=\"\">ペット用品</a></div>\n    <div class=\"category-item\"><a href=\"/collections/kids\"><img src=\"https://img.myshopline.com/image/store/1726464192427/11-6.jpeg?w=1080&h=1080\" alt=\"\">キッズ家具・ベビー用品</a></div>\n</div>\n<div class=\"category-container\">\n    <div class=\"category-item\"><a href=\"/collections/bed\"><img src=\"https://img.myshopline.com/image/store/1726464192427/18-4.jpeg?w=1080&h=1080\" alt=\"\">ベッド</a></div>\n    <div class=\"category-item\"><a href=\"/collections/mattress\"><img src=\"https://img.myshopline.com/image/store/1726464192427/13-5.jpeg?w=1080&h=1080\" alt=\"\">マットレス</a></div>\n    <div class=\"category-item\"><a href=\"/collections/tv-board\"><img src=\"https://img.myshopline.com/image/store/1726464192427/14-6.jpeg?w=1080&h=1080\" alt=\"\">テレビ台</a></div>\n    <div class=\"category-item\"><a href=\"/collections/cupboards\"><img src=\"https://img.myshopline.com/image/store/1726464192427/19-3.jpeg?w=1080&h=1080\" alt=\"\">キッチン収納</a></div>\n    <div class=\"category-item\"><a href=\"/collections/entrance-storage\"><img src=\"https://img.myshopline.com/image/store/1726464192427/17-5.jpeg?w=1080&h=1080\" alt=\"\">玄関収納</a></div>\n    <div class=\"category-item\"><a href=\"/collections/carpet\"><img src=\"https://img.myshopline.com/image/store/1726464192427/16-7.jpeg?w=1080&h=1080\" alt=\"\">ラグ・カーペット</a></div>\n    <div class=\"category-item\"><a href=\"/collections/garden\"><img src=\"https://img.myshopline.com/image/store/1726464192427/21-3.jpeg?w=1080&h=1080\" alt=\"\">屋外家具</a></div>\n    <div class=\"category-item\"><a href=\"/collections/big-sale\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-big-sale.jpg?w=240&h=240\" alt=\"\">SALE</a></div>\n</div>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 0}, "blocks": {}, "block_order": [], "custom_css": [".category-container {max-width: 1500px; margin: 0 auto; padding: 20px 0 0 0; display: flex; flex-wrap: wrap; justify-content: space-between;}", ".category-item {width: 11%; text-align: center;}", ".category-item img {width: 100%; border-radius: 50%; padding: 0 10px;}", ".category-item a {display: block; font-size: 1em; font-weight: bold; text-decoration: none;}", "@media (max-width: 768px) {.category-container {display: none; }}"]}, "1733212567317c332b8e": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n<div class=\"carousel\">\n<div class=\"category-group\">\n<div class=\"sp-category-item\"><a href=\"/collections/sofa\"><img src=\"https://img.myshopline.com/image/store/1726464192427/6-13.jpeg?w=1080&h=1080\" alt=\"\">ソファ</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/chairs\"><img src=\"https://img.myshopline.com/image/store/1726464192427/7-14.jpeg?w=1080&h=1080\" alt=\"\">チェア・椅子</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/tables\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-table-6.jpg?w=240&h=240\" alt=\"\">テーブル</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/desk\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-desk.jpg?w=240&h=240\" alt=\"\">デスク・机</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/storage\"><img src=\"https://img.myshopline.com/image/store/1726464192427/9-9.jpeg?w=1080&h=1080\" alt=\"\">収納家具</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/dresser\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-dresser-6.jpg?w=240&h=240\" alt=\"\">ミラー・ドレッサー</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/pet-supplies\"><img src=\"https://img.myshopline.com/image/store/1726464192427/22-4.jpeg?w=1080&h=1080\" alt=\"\">ペット用品</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/kids\"><img src=\"https://img.myshopline.com/image/store/1726464192427/11-6.jpeg?w=1080&h=1080\" alt=\"\">キッズ家具・ベビー用品</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/bed\"><img src=\"https://img.myshopline.com/image/store/1726464192427/18-4.jpeg?w=1080&h=1080\" alt=\"\">ベッド</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/mattress\"><img src=\"https://img.myshopline.com/image/store/1726464192427/13-5.jpeg?w=1080&h=1080\" alt=\"\">マットレス</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/tv-board\"><img src=\"https://img.myshopline.com/image/store/1726464192427/14-6.jpeg?w=1080&h=1080\" alt=\"\">テレビ台</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/cupboards\"><img src=\"https://img.myshopline.com/image/store/1726464192427/19-3.jpeg?w=1080&h=1080\" alt=\"\">キッチン収納</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/entrance-storage\"><img src=\"https://img.myshopline.com/image/store/1726464192427/17-5.jpeg?w=1080&h=1080\" alt=\"\">玄関収納</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/carpet\"><img src=\"https://img.myshopline.com/image/store/1726464192427/16-7.jpeg?w=1080&h=1080\" alt=\"\">ラグ・カーペット</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/garden\"><img src=\"https://img.myshopline.com/image/store/1726464192427/21-3.jpeg?w=1080&h=1080\" alt=\"\">屋外家具</a></div>\n<div class=\"sp-category-item\"><a href=\"/collections/big-sale\"><img src=\"https://img.myshopline.com/image/store/1726464192427/icon-big-sale.jpg?w=240&h=240\" alt=\"\">SALE</a></div>\n</div>\n</div>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 20}, "blocks": {}, "block_order": [], "custom_css": []}, "1735525845102fdadb45": {"disabled": false, "type": "collection-list", "settings": {"title": "テイスト", "collection_image_ratio": "adapt", "collection_image_shape": "square", "color_scheme": "none", "desktop_grid_cols": 4, "m_cols": "2", "m_rows": "1", "slice_in_mobile": false, "slice_in_pc": false, "max_in_mobile": 8, "button_text": "", "padding_top": 40, "padding_bottom": 20}, "blocks": {"173552584510283df8d1": {"type": "collection", "settings": {"title": "ナチュラル", "image_display_area": "center center", "category": "12267330399310423046894095"}, "blockId": "173552584510283df8d1", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "ナチュラル", "overLimit": false}, "1735525845102f776e6c": {"type": "collection", "settings": {"title": "", "image_display_area": "center center", "category": "12267330397778998772264095"}, "blockId": "1735525845102f776e6c", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "シンプルモダン", "overLimit": false}, "173552584510269a56e7": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12267330403188979844204095"}, "blockId": "173552584510269a56e7", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "北欧風", "overLimit": false}, "17355258451025acb441": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12269342827918160120654095"}, "blockId": "17355258451025acb441", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "ヴィンテージ", "overLimit": false}, "1735525845102a76c223": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12266502797718200051224095"}, "blockId": "1735525845102a76c223", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "ラグジュアリー", "overLimit": false}, "173552584510200839bc": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12267330399578187822854095"}, "blockId": "173552584510200839bc", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "パステル", "overLimit": false}, "17355258451029c3a6f9": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12267330403521168718394095"}, "blockId": "17355258451029c3a6f9", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "ジャパンディ", "overLimit": false}, "173552584510262db3b6": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12267330395719427734414095"}, "blockId": "173552584510262db3b6", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "カラフル", "overLimit": false}, "1744261632889eb8e5ee": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12266502798008110325254095"}, "blockId": "1744261632889eb8e5ee", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "アジアン", "overLimit": false}, "1744261635506e6f7624": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12266502797978414672354095"}, "blockId": "1744261635506e6f7624", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "クラシック", "overLimit": false}, "174426172456915992fe": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12269342783140945028034095"}, "blockId": "174426172456915992fe", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "ポップ", "overLimit": false}, "1744261738177a32a838": {"type": "collection", "settings": {"image_display_area": "center center", "category": "12267330395100013332004095"}, "blockId": "1744261738177a32a838", "sectionId": "1735525845102fdadb45", "canAddBlock": true, "formatName": "インダストリアル", "overLimit": false}}, "block_order": ["173552584510283df8d1", "1735525845102f776e6c", "173552584510269a56e7", "17355258451025acb441", "1735525845102a76c223", "173552584510200839bc", "17355258451029c3a6f9", "173552584510262db3b6", "1744261632889eb8e5ee", "1744261635506e6f7624", "174426172456915992fe", "1744261738177a32a838"], "custom_css": [".button.button--link {padding: 12px 0 !important;}", "@media screen and (max-width: 960px) {div#Slider-collection_list {justify-content: space-around; } div#Slider-collection_list .col {background: #fff; margin-top: 10px !important; width: 50% !important; padding-left: calc(var(--grid-horizontal-space) * 0.5) !important; padding-right: calc(var(--grid-horizontal-space) * 0.5) !important; }}"]}, "1736307441040298bb6c": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n    <div class=\"banner-container\">\n    <picture>\n        <source media=\"(max-width: 768px)\" srcset=\"https://img.myshopline.com/image/store/1726464192427/summer-top-coupon-sp.png?w=750&h=120\">\n        <img src=\"https://img.myshopline.com/image/store/1726464192427/summer-top-coupon-pc.png?w=1540&h=76\" alt=\"\">\n    </picture>\n    </div>\n</div>", "color_scheme": "none", "padding_top": 12, "padding_bottom": 12}, "blocks": {}, "block_order": [], "custom_css": [".banner-container {width: 100%; margin: 0; padding: 0;}", ".banner-container img {width: 100%; height: auto; vertical-align: middle;}"]}, "173630824607636e6dce": {"disabled": false, "type": "featured-collection", "settings": {"title": "おすすめアイテム", "heading_size": "title5", "label_1": "", "label_2": "", "label_3": "", "label_4": "", "label_5": "", "label_6": "", "products_to_show": 5, "columns_desktop": 5, "full_width": true, "columns_mobile": "2", "enable_desktop_slider": true, "enable_mobile_slider": true, "button_text": "すべて表示→", "full_in_mobile": false, "product_image_ratio": "100", "image_fill_type": "cover", "image_grid_shape": "square", "show_secondary_image": true, "padding_top": 20, "padding_bottom": 0, "collection_1": "12268621148253897405514095"}, "blocks": {"block-1": {"type": "image", "settings": {}, "blockId": "block-1", "sectionId": "173630824607636e6dce", "canAddBlock": true, "formatName": "图片", "overLimit": true}, "block-2": {"type": "title", "settings": {}, "blockId": "block-2", "sectionId": "173630824607636e6dce", "canAddBlock": true, "formatName": "标题", "overLimit": true}, "block-3": {"type": "price", "settings": {}, "blockId": "block-3", "sectionId": "173630824607636e6dce", "canAddBlock": true, "formatName": "价格", "overLimit": true}}, "block_order": ["block-1", "block-2", "block-3"], "custom_css": [".button.button--link {padding: 12px 0 !important;}"]}, "17370956975501619246": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/carousel-images-with-text/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"title": "シリーズ特集", "sub_title": "impove", "text": "", "image_ratio": "auto", "button_text": "", "link": "", "autoplay": true, "autoplay_speed": 3, "col": 3, "container_bg_color": "#FFFFFF", "show_divider": false, "container_max_width": 1420, "container_config": {"pc": {"left": 40, "right": 40, "top": 20, "bottom": 20, "lock": true}, "mobile": {"left": 20, "right": 20, "top": 20, "bottom": 20, "lock": true}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 25, "title_m_font_size": 18, "title_letter_spacing": 0, "title_line_height": 1.6, "body_font_family": "Montserrat:400", "body_font_color": "#000000", "body_pc_font_size": 16, "body_m_font_size": 16, "body_letter_spacing": 0, "body_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color": "#FFFFFF", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "sub_title_font_family": "Poppins:600", "sub_title_font_color": "#000000", "sub_title_pc_font_size": 20, "sub_title_m_font_size": 20, "sub_title_letter_spacing": 0, "sub_title_line_height": 1, "image_border_thickness": 0, "image_border_opacity": 100, "image_border_radius": 0, "image_border_color": "#000000", "image_shadow_opacity": 0, "image_shadow_offset_x": 0, "image_shadow_offset_y": 4, "image_shadow_blur": 5, "image_shadow_color": "#000000", "best_mobile_style": false, "paginate_style": "arrow", "mobile_paginate_style": "touch"}, "blocks": {"17453974530978479662": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6853429988344732757", "image": "shopline://shop_images/kv_originalslab-series_1080_6992689742601804412.jpg"}}, "1744687853355a9e95e4": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://collections/12266502798826670707374095", "image": "shopline://shop_images/kv_premium-wood2.jpg"}}, "17419175767737dfb484": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://collections/12268110620034919331354095", "image": "shopline://shop_images/premium-bed-special-series.jpg"}}, "17395249442616f7c0cf": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6733039634286905195", "image": "shopline://shop_images/woody-prime-sofa.jpg"}}, "1738553175695b09c777": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6843352906126335910", "image": "shopline://shop_images/wide-bed-series_1080_608.jpg"}}, "173709569755024f59d9": {"type": "image", "icon": "image", "settings": {"title": "", "description": "", "link": "shopline://collections/12266502796763408686054095", "image": "shopline://shop_images/kv-liveal-serise.png"}, "blockId": "173709569755024f59d9"}, "1737095697550cb41e6a": {"type": "image", "icon": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6782089292514345992", "image": "shopline://shop_images/craft_special.jpg"}, "blockId": "1737095697550cb41e6a"}, "17370956975500ba92c2": {"type": "image", "icon": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6650278816911205907", "image": "shopline://shop_images/kv_premium-wood-series_3.png"}, "blockId": "17370956975500ba92c2"}, "17482236806837e7ef07": {"type": "image", "settings": {"title": "", "description": "", "link": "shopline://pages/6941567856119909312", "image": "shopline://shop_images/kv_woody-aura-3_7011125928173260390.jpg"}, "block_order": []}}, "block_order": ["17482236806837e7ef07", "17453974530978479662", "1744687853355a9e95e4", "17419175767737dfb484", "17395249442616f7c0cf", "1738553175695b09c777", "173709569755024f59d9", "1737095697550cb41e6a", "17370956975500ba92c2"], "custom_css": [".advc-carousel-images-with-text.advc-container {margin-left: auto !important; margin-right: auto !important; padding-left: var(--page-padding) !important; padding-right: var(--page-padding) !important; max-width: calc(var(--page-padding) * 2 + var(--page-width)) !important; width: 100% !important;}", ".advc-carousel-images-with-text__head{margin-bottom: 10px;}"]}, "173854623505157f63ad": {"disabled": true, "type": "shopline://apps/高阶组件库/sections/featured-carousel/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_image_height": "auto", "mb_image_height": "auto", "autoplay": false, "scroll_parallax": false, "pagination_type": "left_right_arrow", "transition_effect": "simple", "autoplay_speed": 7, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 10, "bottom": 0, "lock": false}, "mobile": {"left": 0, "right": 0, "top": 4, "bottom": 4, "lock": false}}}, "blocks": {"173854623505274d512e": {"type": "image", "icon": "image", "settings": {"pc_image_area": "center", "mb_image_area": "center", "pc_sub_title": "", "pc_title": "", "pc_body": "", "pc_text_position": "left", "pc_mask": 0, "mb_sub_title": "", "mb_title": "", "mb_body": "", "mb_text_position": "left", "mb_mask": 0, "button_text1": "", "jump_link1": "", "button_text2": "", "jump_link2": "", "title_font_family": "Poppins:600", "title_font_color": "#ffffff", "title_pc_font_size": 32, "title_m_font_size": 24, "title_letter_spacing": 0, "title_line_height": 1.2, "body_font_family": "Montserrat:400", "body_font_color": "#ffffff", "body_pc_font_size": 16, "body_m_font_size": 14, "body_letter_spacing": 0, "body_line_height": 1.6, "sub_title_font_family": "Montserrat:400", "sub_title_font_color": "#ffffff", "sub_title_pc_font_size": 16, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 300, "sub_title_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color1": "#1c1d1d", "btn_font_color2": "#1c1d1d", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background1": "#ffffff", "btn_background2": "#ffffff", "btn_style1": "primary", "btn_style2": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color1": "#ffffff", "btn_border_color2": "#ffffff", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "pc_image": "shopline://shop_images/Springsaletop_coupon_pc_6905687870020527423.png", "mb_image": "shopline://shop_images/Springsaletop_coupon_sp_6909718972628478804.png"}, "blockId": "173854623505274d512e"}}, "block_order": ["173854623505274d512e"], "custom_css": [".advc-featured-carousel {margin-left: auto; margin-right: auto; padding-left: var(--page-padding); padding-right: var(--page-padding); max-width: calc(var(--page-padding) * 2 + var(--page-width)); width: 100%;}"]}, "1738547925280e050842": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/scroll-banner/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_show": true, "mb_show": true, "title_font_family": "Inter:600", "title_font_color": "#ffffff", "title_pc_font_size": 22, "title_m_font_size": 20, "title_letter_spacing": 0, "title_line_height": 1.1, "icon": "line", "container_bg_color": "#ffd867", "container_bg_color_gradient": "radial-gradient(#46dde5 50%,rgba(251,190,204,0.24) 100%)", "pc_height": 55, "mb_height": 40, "enabel_scroll": true, "title1_style_type": "solid_font", "title2_style_type": "solid_font", "title3_style_type": "solid_font", "title4_style_type": "solid_font", "title1": "夏先取りセール", "title2": "まとめ買いでお得に", "title3": "夏先取りセール", "title4": "まとめ買いでお得に", "icon_color": "#fbe274", "pc_scroll_interval": 20, "mb_scroll_interval": 18, "image": "shopline://shop_images/summersale-logo.png"}, "blocks": {}, "block_order": [], "custom_css": [".advc-title {font-size: 18px;}", "@media only screen and (max-width: 750px) {.advc-title {font-size: 14px; }}", ".advc-scroll-banner--icon {aspect-ratio: 1407 / 203;}"]}, "17385659043899d10384": {"disabled": true, "type": "shopline://apps/高阶组件库/sections/media-hover-switch/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_show": true, "mb_show": true, "title": "高級木材シリーズ", "sub_title": "Use the floating method to view pictures, and the novel interaction attracts customers to click", "title_text_align": "left", "content_text_align": "left", "slide_image_height": 370, "container_bg_color": "#FFFFFF", "container_max_width": 1500, "container_config": {"pc": {"left": 40, "right": 40, "top": 20, "bottom": 20, "lock": true}, "mobile": {"left": 20, "right": 20, "top": 20, "bottom": 20, "lock": true}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 10, "bottom": 10, "lock": true}}, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 25, "title_m_font_size": 18, "title_letter_spacing": 0, "title_line_height": 1.2, "sub_title_font_family": "Inter:600", "sub_title_font_color": "#ffffff", "sub_title_pc_font_size": 18, "sub_title_m_font_size": 16, "sub_title_letter_spacing": 0, "sub_title_line_height": 1.2, "body_font_family": "Inter:regular", "body_font_color": "#ffffff", "body_pc_font_size": 14, "body_m_font_size": 12, "body_letter_spacing": 0, "body_line_height": 1.6, "image_border_thickness": 0, "image_border_opacity": 100, "image_border_radius": 12, "image_border_color": "#000000"}, "blocks": {"1738565904389d552084": {"type": "image_text", "settings": {"description": "趣きある重厚な佇まいに目を奪われる「ツゲ材」", "sub_title": "ツゲ材", "link": "shopline://pages/6849074988109800176", "image": "shopline://shop_images/boxwood_feature-pc_6853360124019470393.jpg", "mobile_image": "shopline://shop_images/boxwood_feature-sp_6853360125277761626.jpg"}, "blockId": "1738565904389d552084"}, "173856590438919913a9": {"type": "image_text", "settings": {"description": "時間とともに深くなる優雅な味わいを持つ「チェリー材」", "sub_title": "チェリー材", "link": "shopline://pages/6849078822559294194", "mobile_image": "shopline://shop_images/cherrywood_feature-sp_6853360127693680658.jpg", "image": "shopline://shop_images/cherrywood_feature-pc_6853360126552829981.jpg"}, "blockId": "173856590438919913a9"}, "17394099240746ffd10b": {"type": "image_text", "settings": {"description": "美しく濃い色合いで高級感があふれる「ウォールナット材」", "sub_title": "ウォールナット材", "link": "shopline://pages/6849077043520085063", "mobile_image": "shopline://shop_images/walnut_wood-sp_6853360130260594768.jpg", "image": "shopline://shop_images/walnut_wood-pc_6853360128935194704.jpg"}}, "173856590438904fb50f": {"type": "image_text", "settings": {"description": "重厚感と高級感を併せ持つモダンな「ホワイトアッシュ材」", "sub_title": "ホワイトアッシュ材", "link": "shopline://pages/6863290147468483811", "image": "shopline://shop_images/White-ash-wood-pc.jpg", "mobile_image": "shopline://shop_images/White-ash-wood-sp.jpg"}, "blockId": "173856590438904fb50f", "disabled": false}}, "block_order": ["1738565904389d552084", "173856590438919913a9", "17394099240746ffd10b", "173856590438904fb50f"], "custom_css": [".advc-media-hover-switch h3 {margin-bottom: 20px;}"]}, "1738658825572d68752a": {"disabled": true, "type": "shopline://apps/高阶组件库/sections/carousel-images-with-text/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"title": "商品特集", "sub_title": "impove", "text": "", "image_ratio": "auto", "button_text": "", "link": "", "autoplay": true, "autoplay_speed": 3, "col": 4, "container_bg_color": "#FFFFFF", "show_divider": false, "container_max_width": 1420, "container_config": {"pc": {"left": 40, "right": 40, "top": 20, "bottom": 20, "lock": true}, "mobile": {"left": 20, "right": 20, "top": 20, "bottom": 20, "lock": true}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 25, "title_m_font_size": 18, "title_letter_spacing": 0, "title_line_height": 1.6, "body_font_family": "Inter", "body_font_color": "#000000", "body_pc_font_size": 16, "body_m_font_size": 16, "body_letter_spacing": 0, "body_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color": "#FFFFFF", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "sub_title_font_family": "Inter:600", "sub_title_font_color": "#000000", "sub_title_pc_font_size": 20, "sub_title_m_font_size": 20, "sub_title_letter_spacing": 0, "sub_title_line_height": 1, "image_border_thickness": 0, "image_border_opacity": 100, "image_border_radius": 0, "image_border_color": "#000000", "image_shadow_opacity": 0, "image_shadow_offset_x": 0, "image_shadow_offset_y": 4, "image_shadow_blur": 5, "image_shadow_color": "#000000", "best_mobile_style": false, "paginate_style": "arrow", "mobile_paginate_style": "touch"}, "blocks": {"1739927894457c79b129": {"type": "image", "settings": {"title": "", "description": "<strong>電動リクライニングソファ</strong>｜くつろぎの完成形を、ボタンひとつで", "link": "shopline://collections/12269705842022540039534095", "image": "shopline://shop_images/electric-reclining-sofa.jpg"}}, "17399279210003b474bf": {"type": "image", "settings": {"title": "", "description": "<strong>イタリアンモダンソファ</strong>｜洗練されたデザインで、日常に贅沢なひとときを演出", "link": "shopline://collections/12269705854258667032194095", "image": "shopline://shop_images/ Italian-modern-sofa.jpg"}}, "17447662416043b5e471": {"type": "image", "settings": {"title": "", "description": "<strong>ハンガーラック</strong>｜省スペースで叶えるおしゃれ収納術", "link": "shopline://collections/12266502792190610689684095", "image": "shopline://shop_images/2_6953118614358855852.jpg"}}, "1739927965135303cc5c": {"type": "image", "settings": {"title": "", "description": "<strong>デザイナーズチェア</strong>｜美しさと快適さを兼ね備えたアイテムで空間が変わる", "link": "shopline://collections/12269229961307422791794095", "image": "shopline://shop_images/Special20_6926103610162687205.jpeg"}}, "17399279906385606b13": {"type": "image", "settings": {"title": "", "description": "<strong>おしゃれ照明｜</strong>スペシャルなライトで毎日の暮らしを特別なものに", "link": "shopline://collections/12269230126112364796244095", "image": "shopline://shop_images/Special21_6926104123646160112.jpeg"}}, "173709644492625559e9": {"type": "image", "settings": {"title": "", "description": "<strong>昇降式テーブル・デスク</strong>｜高さを調整でき、快適でリラックスな作業環境を提供", "link": "shopline://collections/12266502790861184097074095", "image": "shopline://shop_images/Special-17.jpg"}}, "1737096516402f2194df": {"type": "image", "settings": {"title": "", "description": "<strong>プラスチック収納</strong>｜軽量&amp;大容量でスッキリとお部屋を整理整頓", "link": "shopline://collections/12268749840282495257134095", "image": "shopline://shop_images/Special-19.jpg"}}, "173709654189184adda6": {"type": "image", "settings": {"title": "", "description": "<strong>ミラー付きドレッサー</strong>｜毎朝の身支度をより楽しくする「わたしだけの空間」", "link": "shopline://collections/12268750070724267907214095", "image": "shopline://shop_images/Special17.jpg"}}, "17370965803527f1ceb3": {"type": "image", "settings": {"title": "", "description": "<strong>レザーベッド</strong>｜美しい革素材で、寝室空間をランクアップ", "link": "shopline://collections/12268750767429871239934095", "image": "shopline://shop_images/Special-21.jpg"}}, "17370966102280c062f7": {"type": "image", "settings": {"title": "", "description": "<strong>スタイリングミラー</strong>｜お出かけ前からおしゃれを楽しもう", "link": "shopline://collections/12268753240538813594324095", "image": "shopline://shop_images/Special-11.jpg"}}}, "block_order": ["1739927894457c79b129", "17399279210003b474bf", "17447662416043b5e471", "1739927965135303cc5c", "17399279906385606b13", "173709644492625559e9", "1737096516402f2194df", "173709654189184adda6", "17370965803527f1ceb3", "17370966102280c062f7"], "custom_css": [".advc-carousel-images-with-text.advc-container {margin-left: auto !important; margin-right: auto !important; padding-left: var(--page-padding) !important; padding-right: var(--page-padding) !important; max-width: calc(var(--page-padding) * 2 + var(--page-width)) !important; width: 100% !important;}", ".advc-carousel-images-with-text__head {margin-bottom: 10px;}", ".advc-carousel-images-with-text__item-description {-webkit-line-clamp: 2; text-align: left;}"]}, "1739496159203aab3682": {"disabled": false, "type": "featured-collection-with-banner", "settings": {"title": "夏先取りセール", "heading_size": "title5", "show_collections_desc": false, "show_collection_image": false, "image_opacticy": 0, "text_align": "text-center", "collection_title": "", "collection_description": "", "collection_text_color": "#0770ab", "collection_button_text": "すべて表示→", "products_num": 9, "pc_cols": 5, "mobile_cols": "2", "slice_in_pc": true, "slice_in_mobile": true, "button_text": "", "product_image_ratio": "100", "image_fill_type": "cover", "show_secondary_image": true, "padding_top": 30, "padding_bottom": 30, "image": "shopline://shop_images/summersale-back3.png", "product_categories": "12267330399678683347744095"}, "blocks": {"1739496159203902e7f6": {"type": "image", "settings": {}, "blockId": "1739496159203902e7f6", "sectionId": "1739496159203aab3682", "canAddBlock": true, "formatName": "图片", "overLimit": true}, "17394961592037960495": {"type": "title", "settings": {}, "blockId": "17394961592037960495", "sectionId": "1739496159203aab3682", "canAddBlock": true, "formatName": "标题", "overLimit": true}, "17394961592031a934db": {"type": "price", "settings": {}, "blockId": "17394961592031a934db", "disabled": false, "sectionId": "1739496159203aab3682", "canAddBlock": true, "formatName": "价格", "overLimit": true}}, "block_order": ["1739496159203902e7f6", "17394961592037960495", "17394961592031a934db"], "custom_css": [".featured-collection-with-banner__item-content {top: 82%;}"]}, "174108181967742ffc0c": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/featured-carousel/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_image_height": "auto", "mb_image_height": "auto", "autoplay": true, "scroll_parallax": false, "pagination_type": "left_right_arrow", "transition_effect": "simple", "mobile_pagination_type": "touch", "autoplay_speed": 5, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 40, "lock": false}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 40, "lock": false}}}, "blocks": {"174218268800929bdc29": {"type": "image", "settings": {"pc_image_area": "center", "mb_image_area": "center", "pc_sub_title": "", "pc_title": "", "pc_body": "", "pc_text_position": "center", "pc_mask": 0, "mb_sub_title": "", "mb_title": "", "mb_body": "", "mb_text_position": "center", "mb_mask": 0, "button_text1": "", "btn_font_color1": "#FFFFFF", "btn_background1": "#111111", "btn_border_color1": "#000000", "btn_style1": "primary", "button_text2": "", "btn_font_color2": "#FFFFFF", "btn_background2": "#111111", "btn_border_color2": "#000000", "btn_style2": "primary", "sub_title_font_family": "Montserrat:400", "sub_title_font_color": "#ffffff", "sub_title_pc_font_size": 16, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 300, "sub_title_line_height": 1.6, "title_font_family": "Poppins:600", "title_font_color": "#ffffff", "title_pc_font_size": 32, "title_m_font_size": 36, "title_letter_spacing": 0, "title_line_height": 1.2, "body_font_family": "Montserrat:400", "body_font_color": "#ffffff", "body_pc_font_size": 16, "body_m_font_size": 14, "body_letter_spacing": 0, "body_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_hover_animation": 1, "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "jump_link1": "shopline://pages/6875128058451097221", "mb_image": "shopline://shop_images/mycoordi-sp.png", "pc_image": "shopline://shop_images/mycoordi-pc.png"}, "disabled": false}, "17410818196778d7e1b1": {"type": "image", "icon": "image", "settings": {"pc_image_area": "center", "mb_image_area": "center", "pc_sub_title": "", "pc_title": "", "pc_body": "", "pc_text_position": "left", "pc_mask": 0, "mb_sub_title": "", "mb_title": "", "mb_body": "", "mb_text_position": "left", "mb_mask": 0, "button_text1": "", "jump_link1": "https://onelink.onecommerce.io/9LwlnydE", "button_text2": "", "jump_link2": "", "title_font_family": "Poppins:600", "title_font_color": "#ffffff", "title_pc_font_size": 32, "title_m_font_size": 36, "title_letter_spacing": 0, "title_line_height": 1.2, "body_font_family": "Montserrat:400", "body_font_color": "#ffffff", "body_pc_font_size": 16, "body_m_font_size": 14, "body_letter_spacing": 0, "body_line_height": 1.6, "sub_title_font_family": "Montserrat:400", "sub_title_font_color": "#ffffff", "sub_title_pc_font_size": 16, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 300, "sub_title_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color1": "#1c1d1d", "btn_font_color2": "#1c1d1d", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background1": "#ffffff", "btn_background2": "#ffffff", "btn_style1": "primary", "btn_style2": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color1": "#ffffff", "btn_border_color2": "#ffffff", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "pc_image": "shopline://shop_images/Download-pc.jpg", "mb_image": "shopline://shop_images/Download-sp.jpg"}, "blockId": "17410818196778d7e1b1", "disabled": false}}, "block_order": ["174218268800929bdc29", "17410818196778d7e1b1"], "custom_css": [".advc-featured-carousel {margin-left: auto; margin-right: auto; padding-left: var(--page-padding); padding-right: var(--page-padding); max-width: calc(var(--page-padding) * 2 + var(--page-width)); width: 100%;}", ".advc-featured-carousel__swiper {border-radius: var(--border-radius);}"]}, "1747016484876b0fb132": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "インテリアコーディネート集", "description": "", "button_text": "もっと見る→", "jump_link": "shopline://pages/6992419426302431745", "show_columns": 2, "image_ratio": "50%", "anchor_quick_view": false, "anchor_show_type": "click", "padding_top": 30, "padding_bottom": 30}, "blocks": {"17470164848764ecea4d": {"type": "image", "settings": {"product_butotn_text": "商品を見る", "horizontal_axis_position1": 12, "vertical_axis_position1": 78, "horizontal_axis_position2": 46, "vertical_axis_position2": 68, "horizontal_axis_position3": 56, "vertical_axis_position3": 76, "horizontal_axis_position4": 61, "vertical_axis_position4": 100, "horizontal_axis_position5": 90, "vertical_axis_position5": 91, "image": "shopline://shop_images/未命名方案-25-20250509-154212.jpg", "product1": "16068217676586536240644095", "product2": "16068217802811766713204095", "product4": "16069100371037753269584095", "product3": "16068634624736024366254095", "product5": "16068852601793164114174095"}, "blockId": "17470164848764ecea4d", "disabled": false, "sectionId": "1747016484876b0fb132", "canAddBlock": true, "formatName": "ウォールナット材 ラグジュアリーリクライニングソファ", "overLimit": false}, "17470164848761fce22a": {"type": "image", "settings": {"product_butotn_text": "商品を見る", "horizontal_axis_position1": 70, "vertical_axis_position1": 34, "horizontal_axis_position2": 38, "vertical_axis_position2": 85, "horizontal_axis_position3": 50, "vertical_axis_position3": 70, "horizontal_axis_position4": 80, "vertical_axis_position4": 80, "horizontal_axis_position5": 56, "vertical_axis_position5": 85, "image": "shopline://shop_images/1_6992503229620444119.jpeg", "product5": "16067330469137364216304095", "product3": "16068809655795756629554095", "product4": "16068217800711258861784095", "product1": "16068256033973760671114095", "product2": "16068110891379545028174095"}, "blockId": "17470164848761fce22a", "disabled": false, "sectionId": "1747016484876b0fb132", "canAddBlock": true, "formatName": "天然木組み合わせ本棚", "overLimit": false}, "1747121145558a51db56": {"type": "image", "settings": {"product_butotn_text": "商品を見る", "horizontal_axis_position1": 8, "vertical_axis_position1": 89, "horizontal_axis_position2": 83, "vertical_axis_position2": 78, "horizontal_axis_position3": 32, "vertical_axis_position3": 77, "horizontal_axis_position4": 52, "vertical_axis_position4": 78, "horizontal_axis_position5": 53, "vertical_axis_position5": 64, "image": "shopline://shop_images/モノトーン_6992628052560468996.jpeg", "product4": "16068110700445935023354095", "product2": "16068256033266265470154095", "product3": "16068110650095160547194095", "product1": "16068217801400970624294095", "product5": "16067330447566897200694095"}, "disabled": false, "blockId": "1747121145558a51db56", "sectionId": "1747016484876b0fb132", "canAddBlock": true, "formatName": "可動式サイドワゴン", "overLimit": false}, "17471216278389359ed3": {"type": "image", "settings": {"product_butotn_text": "商品を見る", "horizontal_axis_position1": 22, "vertical_axis_position1": 78, "horizontal_axis_position2": 53, "vertical_axis_position2": 65, "horizontal_axis_position3": 51, "vertical_axis_position3": 80, "horizontal_axis_position4": 80, "vertical_axis_position4": 78, "horizontal_axis_position5": 89, "vertical_axis_position5": 64, "product4": "", "product3": "", "image": "shopline://shop_images/南国風.jpeg", "product2": "16068110771196293756244095", "product1": "16068217676586536240644095", "product5": "16068707491905460488024095"}, "disabled": false, "blockId": "17471216278389359ed3", "sectionId": "1747016484876b0fb132", "canAddBlock": true, "formatName": "ウォールナット材 ラグジュアリーリクライニングソファ", "overLimit": false, "custom_css": []}, "1747122044728b60db77": {"type": "image", "settings": {"product_butotn_text": "商品を見る", "horizontal_axis_position1": 53, "vertical_axis_position1": 71, "horizontal_axis_position2": 41, "vertical_axis_position2": 78, "horizontal_axis_position3": 21, "vertical_axis_position3": 80, "horizontal_axis_position4": 75, "vertical_axis_position4": 78, "horizontal_axis_position5": 58, "vertical_axis_position5": 85, "image": "shopline://shop_images/インダストリアル.jpeg", "product1": "16068110650613409201694095", "product2": "16068255888073386086304095", "product5": "16068941324791680903584095", "product4": "16068217696486495447814095", "product3": "16068217696486495447814095"}, "blockId": "1747122044728b60db77", "sectionId": "1747016484876b0fb132", "canAddBlock": true, "formatName": "ウォールナット材 天然木収納付きテレビボード", "overLimit": false}}, "block_order": ["17470164848764ecea4d", "17470164848761fce22a", "1747121145558a51db56", "1747122044728b60db77", "17471216278389359ed3"], "custom_css": []}, "1740634905286447dfcb": {"disabled": false, "type": "text-columns-with-image", "settings": {"title": "高級木材シリーズ", "title_font_size": "title5", "image_width": "100%", "image_ratio": "auto", "pc_cols": 4, "text_align": "center", "show_block_bg": "false", "button_text": "", "jump_link": "", "show_touch": false, "color_scheme": "none", "padding_top": 40, "padding_bottom": 12}, "blocks": {"17406348855008347c35": {"type": "item", "settings": {"title": "", "description": "", "button_text": "チェリー材", "jump_link": "shopline://pages/6849078822559294194", "image": "shopline://shop_images/cherrywood_feature-pc_6853360126552829981.jpg"}, "disabled": false, "blockId": "17406348855008347c35", "sectionId": "1740634905286447dfcb", "canAddBlock": true, "formatName": "图片", "overLimit": false, "custom_css": []}, "17406348808359a221f0": {"type": "item", "settings": {"title": "", "description": "", "button_text": "ツゲ材", "jump_link": "shopline://pages/6849074988109800176", "image": "shopline://shop_images/boxwood_feature-pc_6853360124019470393.jpg"}, "disabled": false, "blockId": "17406348808359a221f0", "sectionId": "1740634905286447dfcb", "canAddBlock": true, "formatName": "图片", "overLimit": false, "custom_css": []}, "17406348915498de5d20": {"type": "item", "settings": {"title": "", "description": "", "button_text": "ウォールナット材", "jump_link": "shopline://pages/6849077043520085063", "image": "shopline://shop_images/walnut_wood-pc_6853360128935194704.jpg"}, "disabled": false, "blockId": "17406348915498de5d20", "sectionId": "1740634905286447dfcb", "canAddBlock": true, "formatName": "图片", "overLimit": false, "custom_css": []}, "17406348886202ff2458": {"type": "item", "settings": {"title": "", "description": "", "button_text": "ホワイトアッシュ材", "jump_link": "shopline://pages/6863290147468483811", "image": "shopline://shop_images/White-ash-wood-pc.jpg"}, "disabled": false, "blockId": "17406348886202ff2458", "sectionId": "1740634905286447dfcb", "canAddBlock": true, "formatName": "图片", "overLimit": false, "custom_css": []}}, "block_order": ["17406348855008347c35", "17406348808359a221f0", "17406348915498de5d20", "17406348886202ff2458"], "custom_css": [".text-columns-with-images-item__content {padding-right: 0; }", "}"]}, "1748346054314adaf21d": {"disabled": false, "type": "collection-list", "settings": {"title": "商品特集", "collection_image_ratio": "adapt", "collection_image_shape": "square", "color_scheme": "none", "desktop_grid_cols": 4, "m_cols": "2", "m_rows": "1", "slice_in_mobile": true, "slice_in_pc": true, "max_in_mobile": 10, "button_text": "もっと見る→", "padding_top": 40, "padding_bottom": 20}, "blocks": {"173552584510283df8d1": {"type": "collection", "settings": {"title": "", "image_display_area": "center center", "category": "12270114303113479238394095"}, "blockId": "173552584510283df8d1", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "ステンレス家具｜クールな素材感に、丸みと温もりを添えたデザイン", "overLimit": false, "custom_css": []}, "1735525845102f776e6c": {"type": "collection", "settings": {"title": "", "image_display_area": "center center", "category": "12269705842022540039534095"}, "blockId": "1735525845102f776e6c", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "電動リクライニングソファ｜くつろぎの完成形を、ボタンひとつで", "overLimit": false, "custom_css": []}, "173552584510269a56e7": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12269705854258667032194095"}, "blockId": "173552584510269a56e7", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "イタリアンモダンソファ｜洗練されたデザインで、日常に贅沢なひとときを演出", "overLimit": false, "custom_css": []}, "17355258451025acb441": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12266502792190610689684095"}, "blockId": "17355258451025acb441", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "ハンガーラック｜省スペースで叶えるおしゃれ収納術", "overLimit": false, "custom_css": []}, "1735525845102a76c223": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12269229961307422791794095"}, "blockId": "1735525845102a76c223", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "デザイナーズチェア｜美しさと快適さを兼ね備えたアイテムで空間が変わる", "overLimit": false, "custom_css": []}, "173552584510200839bc": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12269230126112364796244095"}, "blockId": "173552584510200839bc", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "おしゃれ照明｜スペシャルなライトで毎日の暮らしを特別なものに", "overLimit": false, "custom_css": []}, "17355258451029c3a6f9": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12266502790861184097074095"}, "blockId": "17355258451029c3a6f9", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "昇降式テーブル・デスク｜高さを調整でき、快適でリラックスな作業環境を提供", "overLimit": false, "custom_css": []}, "173552584510262db3b6": {"type": "collection", "settings": {"image_display_area": "center center", "title": "", "category": "12268749840282495257134095"}, "blockId": "173552584510262db3b6", "sectionId": "1748346054314adaf21d", "canAddBlock": true, "formatName": "プラスチック収納｜軽量&大容量でスッキリとお部屋を整理整頓", "overLimit": false, "custom_css": []}}, "block_order": ["173552584510283df8d1", "1735525845102f776e6c", "173552584510269a56e7", "17355258451025acb441", "1735525845102a76c223", "173552584510200839bc", "17355258451029c3a6f9", "173552584510262db3b6"], "custom_css": [".button.button--link {padding: 12px 0 !important;}", ".card__content {display:none;}", "h4 {margin-top: 12px;}", "@media screen and (max-width: 960px) {div#Slider-collection_list {justify-content: space-around; } div#Slider-collection_list .col {background: #fff; margin-top: 10px !important; width: 50%; padding-left: calc(var(--grid-horizontal-space) * 0.5) !important; padding-right: calc(var(--grid-horizontal-space) * 0.5) !important; }}"]}}, "order": ["1733212444478604afeb", "1738547925280e050842", "173854623505157f63ad", "1736307441040298bb6c", "173321252133581cc3a4", "1733212567317c332b8e", "1739496159203aab3682", "featured-collection", "17370956975501619246", "17385659043899d10384", "1740634905286447dfcb", "1748346054314adaf21d", "1738658825572d68752a", "1747016484876b0fb132", "1735525845102fdadb45", "173630824607636e6dce", "featured-collection2", "17273238961427984f2f", "174108181967742ffc0c", "1727331691879fda4a51", "logo-list"]}